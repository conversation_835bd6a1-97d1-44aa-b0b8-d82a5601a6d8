"""
Edge Server Model for Heterogeneous Multi-Edge Server DNN Inference Optimization
Implements server capacity management and load balancing
"""

import numpy as np
from typing import Dict, Any, List, Optional
from config import config

class EdgeServer:
    """
    Edge server model with capacity constraints and load management
    Based on laptop 4060 specifications from technical document
    """
    
    def __init__(self, server_id: int):
        """
        Initialize edge server
        
        Args:
            server_id: Unique server identifier (0-3)
        """
        self.server_id = server_id
        self.config = config.server
        
        # Server specifications
        self.f_server = np.random.uniform(
            self.config.f_server_min,
            self.config.f_server_max
        )  # Hz
        self.g_server = self.config.g_server  # FLOPS per cycle
        self.max_load = self.config.max_load  # Maximum load ratio
        self.server_type = self.config.server_type
        
        # Time slot management (borrowed from original project concept)
        self.time_slot_duration = 0.05  # 20ms time slots for fine-grained scheduling
        self.compute_capacity_per_slot = self.f_server * self.g_server * self.max_load * self.time_slot_duration
        
        # Current state
        self.current_load = 0.0  # Current load ratio (0-1)
        self.active_tasks = []  # List of active task information
        self.total_flops_allocated = 0.0  # Total FLOPs currently allocated
        self.current_slot_utilization = 0.0  # Current time slot utilization
        
        # Performance tracking
        self.load_history = []
        self.task_count_history = []
        self.utilization_history = []
        
    def get_state(self) -> np.ndarray:
        """
        Get current server state
        
        Returns:
            State vector: [load_ratio, available_capacity_ratio]
        """
        load_ratio = self.current_load
        available_capacity_ratio = max(0.0, (self.max_load - self.current_load) / self.max_load)
        
        return np.array([load_ratio, available_capacity_ratio], dtype=np.float32)
    
    def get_available_slot_capacity(self) -> float:
        """
        Get available computational capacity in current time slot
        
        Returns:
            Available FLOPS capacity in current time slot
        """
        return self.compute_capacity_per_slot - (self.current_slot_utilization * self.compute_capacity_per_slot)
    
    def can_accept_task(self, required_flops: float, estimated_time: float = None) -> bool:
        """
        Check if server can accept a new task using time slot-based capacity management
        
        Args:
            required_flops: Required FLOPs for the task
            estimated_time: Estimated execution time in seconds (if None, calculated)
            
        Returns:
            True if server can accept the task, False otherwise
        """
        if estimated_time is None:
            estimated_time = self.calculate_processing_time(required_flops)
        
        # Calculate how many time slots this task will occupy
        slots_needed = max(1, int(np.ceil(estimated_time / self.time_slot_duration)))
        
        # Calculate FLOPS per slot for this task
        flops_per_slot = required_flops / slots_needed
        
        # Check if we have enough capacity in current slot
        available_capacity = self.get_available_slot_capacity()
        
        return flops_per_slot <= available_capacity
    
    def allocate_task(self, task_id: int, required_flops: float, device_id: int, 
                     estimated_time: float = None) -> bool:
        """
        Allocate computational resources for a task using time slot-based management
        
        Args:
            task_id: Unique task identifier
            required_flops: Required FLOPs for the task
            device_id: ID of the device requesting the task
            estimated_time: Estimated execution time in seconds
            
        Returns:
            True if task was allocated successfully, False otherwise
        """
        if estimated_time is None:
            estimated_time = self.calculate_processing_time(required_flops)
            
        if self.can_accept_task(required_flops, estimated_time):
            # Calculate time slot allocation
            slots_needed = max(1, int(np.ceil(estimated_time / self.time_slot_duration)))
            flops_per_slot = required_flops / slots_needed
            
            # Calculate utilization for this task
            task_utilization = flops_per_slot / self.compute_capacity_per_slot
            
            # Add task to active tasks
            task_info = {
                'task_id': task_id,
                'device_id': device_id,
                'required_flops': required_flops,
                'estimated_time': estimated_time,
                'slots_needed': slots_needed,
                'flops_per_slot': flops_per_slot,
                'task_utilization': task_utilization,
                'allocated_time': 0.0
            }
            self.active_tasks.append(task_info)
            self.total_flops_allocated += required_flops
            self.current_slot_utilization += task_utilization
            
            # Update load based on slot utilization
            self._update_load()
            return True
        return False
    
    def deallocate_task(self, task_id: int) -> bool:
        """
        Deallocate computational resources for a completed task using time slot management
        
        Args:
            task_id: Task identifier to deallocate
            
        Returns:
            True if task was deallocated successfully, False if task not found
        """
        for i, task in enumerate(self.active_tasks):
            if task['task_id'] == task_id:
                self.total_flops_allocated -= task['required_flops']
                self.current_slot_utilization -= task.get('task_utilization', 0.0)
                # Ensure utilization doesn't go negative due to floating point errors
                self.current_slot_utilization = max(0.0, self.current_slot_utilization)
                self.active_tasks.pop(i)
                self._update_load()
                return True
        return False
    
    def calculate_processing_time(self, flops: float) -> float:
        """
        Calculate processing time for given FLOPs
        
        Args:
            flops: Number of floating point operations
            
        Returns:
            Processing time in seconds
        """
        # T_edge = C_edge / (f_server * g_server)
        return flops / (self.f_server * self.g_server)
    
    def get_current_utilization(self) -> float:
        """
        Get current server utilization as ratio of maximum capacity
        
        Returns:
            Utilization ratio (0-1)
        """
        # Utilization is directly represented by slot utilization relative to max_load
        return self.current_slot_utilization / self.max_load if self.max_load > 0 else 0.0
    
    def _update_load(self):
        """Update current load based on time slot utilization"""
        # Load is directly represented by current slot utilization
        # This ensures load never exceeds max_load since we check capacity in can_accept_task
        self.current_load = min(self.current_slot_utilization, self.max_load)
    
    def is_overloaded(self) -> bool:
        """Check if server is overloaded beyond maximum capacity"""
        return self.current_load > self.max_load
    
    def get_load_penalty(self) -> float:
        """
        Calculate penalty for server overload
        
        Returns:
            Penalty value (0 if not overloaded, positive if overloaded)
        """
        if self.is_overloaded():
            overload_ratio = (self.current_load - self.max_load) / self.max_load
            return config.environment.server_overload_penalty * overload_ratio
        return 0.0
    
    def update_history(self):
        """Update server state history for tracking"""
        self.load_history.append(self.current_load)
        self.task_count_history.append(len(self.active_tasks))
        self.utilization_history.append(self.get_current_utilization())
    
    def reset(self):
        """Reset server to initial state"""
        self.current_load = 0.0
        self.active_tasks.clear()
        self.total_flops_allocated = 0.0
        self.current_slot_utilization = 0.0
        self.load_history.clear()
        self.task_count_history.clear()
        self.utilization_history.clear()
    
    def get_info(self) -> Dict[str, Any]:
        """Get comprehensive server information"""
        return {
            'server_id': self.server_id,
            'frequency_hz': self.f_server,
            'frequency_ghz': self.f_server / 1e9,
            'current_load': self.current_load,
            'max_load': self.max_load,
            'utilization': self.get_current_utilization(),
            'active_tasks': len(self.active_tasks),
            'available_slot_capacity': self.get_available_slot_capacity(),
            'is_overloaded': self.is_overloaded(),
            'load_penalty': self.get_load_penalty(),
            'server_type': self.server_type,
            # Time slot management information
            'time_slot_duration': self.time_slot_duration,
            'compute_capacity_per_slot': self.compute_capacity_per_slot,
            'current_slot_utilization': self.current_slot_utilization
        }
    
    def get_task_info(self, task_id: int) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task information dictionary or None if task not found
        """
        for task in self.active_tasks:
            if task['task_id'] == task_id:
                return task.copy()
        return None
    
    def get_all_tasks_info(self) -> List[Dict[str, Any]]:
        """Get information about all active tasks"""
        return [task.copy() for task in self.active_tasks]
    
    def __str__(self) -> str:
        """String representation of server state"""
        info = self.get_info()
        return (f"Server {info['server_id']}: "
                f"Load={info['current_load']:.2f}/{info['max_load']:.2f}, "
                f"Tasks={info['active_tasks']}, "
                f"Freq={info['frequency_ghz']:.2f}GHz")
