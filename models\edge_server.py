"""
Edge Server Model for Heterogeneous Multi-Edge Server DNN Inference Optimization
Implements server capacity management and load balancing
"""

import numpy as np
from typing import Dict, Any, List, Optional
from config import config

class EdgeServer:
    """
    Edge server model with capacity constraints and load management
    Based on laptop 4060 specifications from technical document
    """
    
    def __init__(self, server_id: int):
        """
        Initialize edge server
        
        Args:
            server_id: Unique server identifier (0-3)
        """
        self.server_id = server_id
        self.config = config.server
        
        # Server specifications
        self.f_server = np.random.uniform(
            self.config.f_server_min,
            self.config.f_server_max
        )  # Hz
        self.g_server = self.config.g_server  # FLOPS per cycle
        self.max_load = self.config.max_load  # Maximum load ratio
        self.server_type = self.config.server_type
        
        # Current state
        self.current_load = 0.0  # Current load ratio (0-1)
        self.active_tasks = []  # List of active task information
        self.total_flops_allocated = 0.0  # Total FLOPs currently allocated
        
        # Performance tracking
        self.load_history = []
        self.task_count_history = []
        self.utilization_history = []
        
        # Processing rate tracking
        self.current_processing_rate = 0.0  # Current FLOPS/second being processed
        
    def get_state(self) -> np.ndarray:
        """
        Get current server state
        
        Returns:
            State vector: [load_ratio, available_capacity_ratio]
        """
        load_ratio = self.current_load
        available_capacity_ratio = max(0.0, (self.max_load - self.current_load) / self.max_load)
        
        return np.array([load_ratio, available_capacity_ratio], dtype=np.float32)
    
    def get_available_processing_rate(self) -> float:
        """
        Get available computational processing rate in FLOPS/second
        
        Returns:
            Available FLOPS/second processing capacity
        """
        max_rate = self.f_server * self.g_server * self.max_load
        available_rate = max_rate - self.current_processing_rate
        return max(0.0, available_rate)
    
    def get_max_processing_rate(self) -> float:
        """
        Get maximum computational processing rate in FLOPS/second
        
        Returns:
            Maximum FLOPS/second processing capacity
        """
        return self.f_server * self.g_server * self.max_load
    
    def can_accept_task(self, required_flops: float, estimated_time: float = None) -> bool:
        """
        Check if server can accept a new task
        
        Args:
            required_flops: Required FLOPs for the task
            estimated_time: Estimated execution time in seconds (if None, calculated)
            
        Returns:
            True if server can accept the task, False otherwise
        """
        if estimated_time is None:
            estimated_time = self.calculate_processing_time(required_flops)
        
        required_rate = required_flops / estimated_time if estimated_time > 0 else float('inf')
        return required_rate <= self.get_available_processing_rate()
    
    def allocate_task(self, task_id: int, required_flops: float, device_id: int, 
                     estimated_time: float = None) -> bool:
        """
        Allocate computational resources for a task
        
        Args:
            task_id: Unique task identifier
            required_flops: Required FLOPs for the task
            device_id: ID of the device requesting the task
            estimated_time: Estimated execution time in seconds
            
        Returns:
            True if task was allocated successfully, False otherwise
        """
        if estimated_time is None:
            estimated_time = self.calculate_processing_time(required_flops)
            
        if self.can_accept_task(required_flops, estimated_time):
            # Calculate required processing rate
            required_rate = required_flops / estimated_time if estimated_time > 0 else 0.0
            
            # Add task to active tasks
            task_info = {
                'task_id': task_id,
                'device_id': device_id,
                'required_flops': required_flops,
                'estimated_time': estimated_time,
                'required_rate': required_rate,
                'allocated_time': 0.0
            }
            self.active_tasks.append(task_info)
            self.total_flops_allocated += required_flops
            self.current_processing_rate += required_rate
            
            # Update load
            self._update_load()
            return True
        return False
    
    def deallocate_task(self, task_id: int) -> bool:
        """
        Deallocate computational resources for a completed task
        
        Args:
            task_id: Task identifier to deallocate
            
        Returns:
            True if task was deallocated successfully, False if task not found
        """
        for i, task in enumerate(self.active_tasks):
            if task['task_id'] == task_id:
                self.total_flops_allocated -= task['required_flops']
                self.current_processing_rate -= task.get('required_rate', 0.0)
                self.active_tasks.pop(i)
                self._update_load()
                return True
        return False
    
    def calculate_processing_time(self, flops: float) -> float:
        """
        Calculate processing time for given FLOPs
        
        Args:
            flops: Number of floating point operations
            
        Returns:
            Processing time in seconds
        """
        # T_edge = C_edge / (f_server * g_server)
        return flops / (self.f_server * self.g_server)
    
    def get_current_utilization(self) -> float:
        """
        Get current server utilization as ratio of maximum processing rate
        
        Returns:
            Utilization ratio (0-1)
        """
        max_rate = self.get_max_processing_rate()
        if max_rate > 0:
            return self.current_processing_rate / max_rate
        return 0.0
    
    def _update_load(self):
        """Update current load based on processing rate utilization"""
        max_rate = self.get_max_processing_rate()
        if max_rate > 0:
            self.current_load = min(self.current_processing_rate / max_rate, self.max_load)
        else:
            self.current_load = 0.0
    
    def is_overloaded(self) -> bool:
        """Check if server is overloaded beyond maximum capacity"""
        return self.current_load > self.max_load
    
    def get_load_penalty(self) -> float:
        """
        Calculate penalty for server overload
        
        Returns:
            Penalty value (0 if not overloaded, positive if overloaded)
        """
        if self.is_overloaded():
            overload_ratio = (self.current_load - self.max_load) / self.max_load
            return config.environment.server_overload_penalty * overload_ratio
        return 0.0
    
    def update_history(self):
        """Update server state history for tracking"""
        self.load_history.append(self.current_load)
        self.task_count_history.append(len(self.active_tasks))
        self.utilization_history.append(self.get_current_utilization())
    
    def reset(self):
        """Reset server to initial state"""
        self.current_load = 0.0
        self.active_tasks.clear()
        self.total_flops_allocated = 0.0
        self.current_processing_rate = 0.0
        self.load_history.clear()
        self.task_count_history.clear()
        self.utilization_history.clear()
    
    def get_info(self) -> Dict[str, Any]:
        """Get comprehensive server information"""
        return {
            'server_id': self.server_id,
            'frequency_hz': self.f_server,
            'frequency_ghz': self.f_server / 1e9,
            'current_load': self.current_load,
            'max_load': self.max_load,
            'utilization': self.get_current_utilization(),
            'active_tasks': len(self.active_tasks),
            'available_processing_rate': self.get_available_processing_rate(),
            'max_processing_rate': self.get_max_processing_rate(),
            'current_processing_rate': self.current_processing_rate,
            'is_overloaded': self.is_overloaded(),
            'load_penalty': self.get_load_penalty(),
            'server_type': self.server_type
        }
    
    def get_task_info(self, task_id: int) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task information dictionary or None if task not found
        """
        for task in self.active_tasks:
            if task['task_id'] == task_id:
                return task.copy()
        return None
    
    def get_all_tasks_info(self) -> List[Dict[str, Any]]:
        """Get information about all active tasks"""
        return [task.copy() for task in self.active_tasks]
    
    def __str__(self) -> str:
        """String representation of server state"""
        info = self.get_info()
        return (f"Server {info['server_id']}: "
                f"Load={info['current_load']:.2f}/{info['max_load']:.2f}, "
                f"Tasks={info['active_tasks']}, "
                f"Freq={info['frequency_ghz']:.2f}GHz")
