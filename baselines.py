"""
Baseline Algorithms for Comparison with MADDPG
Implements various baseline strategies as specified in the technical document
"""

import numpy as np
import random
from typing import List, Dict, Any, Tu<PERSON>
from config import config
from environment import EdgeComputingEnvironment

class BaselineAlgorithm:
    """Base class for baseline algorithms"""
    
    def __init__(self, name: str):
        self.name = name
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """
        Select actions for all devices
        
        Args:
            env: Edge computing environment
            
        Returns:
            List of actions for each device
        """
        raise NotImplementedError
    
    def reset(self):
        """Reset algorithm state if needed"""
        pass

class LocalOnlyBaseline(BaselineAlgorithm):
    """
    Local-Only baseline: All tasks executed locally without early exit
    """
    
    def __init__(self):
        super().__init__("Local-Only")
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """Select actions for full local execution"""
        actions = []
        
        for i in range(env.num_devices):
            # Full local execution: partition_point=0, any server (not used), max frequency
            action = np.array([
                -1.0,  # partition_point_ratio -> 0 (full local)
                0.0,   # server_selection (not used)
                1.0    # frequency_ratio -> max frequency
            ])
            actions.append(action)
        
        return actions

class EdgeOnlyBaseline(BaselineAlgorithm):
    """
    Edge-Only baseline: All tasks fully offloaded to optimal server
    """
    
    def __init__(self):
        super().__init__("Edge-Only")
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """Select actions for full edge execution"""
        actions = []
        
        # Find server with most available processing rate
        server_rates = [server.get_available_processing_rate() for server in env.servers]
        best_server_idx = np.argmax(server_rates)
        
        for i in range(env.num_devices):
            # Full offload: partition_point=max, best server, min frequency (not used much)
            action = np.array([
                1.0,   # partition_point_ratio -> max (full offload)
                (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,  # Map to [-1,1]
                -1.0   # frequency_ratio -> min frequency (local computation minimal)
            ])
            actions.append(action)
        
        return actions

class GreedyAccuracyBaseline(BaselineAlgorithm):
    """
    Greedy-Accuracy baseline: Always choose full execution for maximum accuracy
    """
    
    def __init__(self):
        super().__init__("Greedy-Accuracy")
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """Select actions prioritizing accuracy"""
        actions = []
        
        for i in range(env.num_devices):
            device = env.devices[i]
            task = env.tasks[i]
            
            # Choose full model execution (no early exit)
            # Decide between local vs edge based on battery level
            battery_ratio = device.get_battery_ratio()
            
            if battery_ratio > 0.7:
                # High battery: local execution with high frequency
                action = np.array([
                    -1.0,  # Full local
                    0.0,   # Server not used
                    1.0    # Max frequency
                ])
            else:
                # Low battery: offload to edge
                # Find least loaded server
                server_loads = [server.current_load for server in env.servers]
                best_server_idx = np.argmin(server_loads)
                
                action = np.array([
                    1.0,   # Full offload
                    (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                    -1.0   # Min frequency
                ])
            
            actions.append(action)
        
        return actions

class GreedyEnergyBaseline(BaselineAlgorithm):
    """
    Greedy-Energy baseline: Choose minimum energy consumption while satisfying accuracy
    """
    
    def __init__(self):
        super().__init__("Greedy-Energy")
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """Select actions prioritizing energy efficiency"""
        actions = []
        
        for i in range(env.num_devices):
            device = env.devices[i]
            task = env.tasks[i]
            
            # Find minimum early exit that satisfies accuracy requirement
            min_exit = config.get_early_exit_for_accuracy(task.accuracy_requirement)
            
            # Always prefer offloading to save local energy
            # Find server with most available processing rate
            server_rates = [server.get_available_processing_rate() for server in env.servers]
            best_server_idx = np.argmax(server_rates)
            
            action = np.array([
                1.0,   # Full offload to save local energy
                (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                -1.0   # Min frequency to save energy
            ])
            
            actions.append(action)
        
        return actions

class RandomBaseline(BaselineAlgorithm):
    """
    Random baseline: Randomly select all decision variables
    """
    
    def __init__(self, seed: int = None):
        super().__init__("Random")
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """Select random actions"""
        actions = []
        
        for i in range(env.num_devices):
            action = np.array([
                np.random.uniform(-1.0, 1.0),  # Random partition point
                np.random.uniform(-1.0, 1.0),  # Random server
                np.random.uniform(-1.0, 1.0)   # Random frequency
            ])
            actions.append(action)
        
        return actions

class AdaptiveGreedyBaseline(BaselineAlgorithm):
    """
    Adaptive Greedy baseline: Adapts strategy based on battery levels and system state
    """
    
    def __init__(self):
        super().__init__("Adaptive-Greedy")
    
    def select_actions(self, env: EdgeComputingEnvironment) -> List[np.ndarray]:
        """Select adaptive actions based on system state"""
        actions = []
        
        # Calculate system-wide battery level
        avg_battery = np.mean([device.get_battery_ratio() for device in env.devices])
        
        # Calculate server load distribution
        server_loads = [server.current_load for server in env.servers]
        avg_server_load = np.mean(server_loads)
        
        for i in range(env.num_devices):
            device = env.devices[i]
            task = env.tasks[i]
            battery_ratio = device.get_battery_ratio()
            
            # Adaptive strategy based on battery and system state
            if battery_ratio > 0.8 and avg_server_load > 0.6:
                # High battery, servers busy -> local execution
                action = np.array([
                    -1.0,  # Local execution
                    0.0,   # Server not used
                    0.5    # Medium frequency
                ])
            elif battery_ratio < 0.3:
                # Low battery -> aggressive offloading
                best_server_idx = np.argmin(server_loads)
                action = np.array([
                    1.0,   # Full offload
                    (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                    -1.0   # Min frequency
                ])
            else:
                # Balanced approach -> partial offloading
                best_server_idx = np.argmin(server_loads)
                partition_ratio = 0.5 - battery_ratio  # More offload when battery lower
                
                action = np.array([
                    partition_ratio,  # Adaptive partitioning
                    (best_server_idx / (env.num_servers - 1)) * 2.0 - 1.0,
                    battery_ratio * 2.0 - 1.0  # Frequency based on battery
                ])
            
            actions.append(action)
        
        return actions

def create_baseline_algorithms() -> Dict[str, BaselineAlgorithm]:
    """Create all baseline algorithms"""
    return {
        'Local-Only': LocalOnlyBaseline(),
        'Edge-Only': EdgeOnlyBaseline(),
        'Greedy-Accuracy': GreedyAccuracyBaseline(),
        'Greedy-Energy': GreedyEnergyBaseline(),
        'Random': RandomBaseline(seed=42),
        'Adaptive-Greedy': AdaptiveGreedyBaseline()
    }

def evaluate_baseline(baseline: BaselineAlgorithm, env: EdgeComputingEnvironment,
                     num_episodes: int = 100, max_steps: int = None) -> Dict[str, Any]:
    """
    Evaluate a baseline algorithm
    
    Args:
        baseline: Baseline algorithm to evaluate
        env: Environment for evaluation
        num_episodes: Number of episodes to run
        max_steps: Maximum steps per episode
        
    Returns:
        Evaluation results dictionary
    """
    if max_steps is None:
        max_steps = env.max_steps
    
    episode_rewards = []
    episode_energies = []
    episode_delays = []
    episode_accuracies = []
    constraint_violations = {
        'accuracy': 0,
        'delay': 0,
        'battery': 0,
        'server_capacity': 0
    }
    
    for episode in range(num_episodes):
        # Reset environment and baseline
        states = env.reset()
        baseline.reset()
        
        episode_reward = 0.0
        episode_energy = 0.0
        episode_delay = 0.0
        step_count = 0
        
        while step_count < max_steps:
            # Get actions from baseline
            actions = baseline.select_actions(env)
            
            # Execute step
            next_states, rewards, dones, info = env.step(actions)
            
            # Accumulate metrics
            episode_reward += sum(rewards)
            episode_energy += sum(info['execution_results']['device_energies'])
            episode_delay += sum(info['execution_results']['device_delays'])
            
            # Count constraint violations
            for violations in info['execution_results']['constraint_violations']:
                for constraint_type, violated in violations.items():
                    if violated:
                        constraint_violations[constraint_type] += 1
            
            step_count += 1
            states = next_states
            
            if all(dones):
                break
        
        episode_rewards.append(episode_reward)
        episode_energies.append(episode_energy)
        episode_delays.append(episode_delay)
        
        # Calculate average accuracy achieved
        avg_accuracy = np.mean([task.achieved_accuracy for task in env.tasks])
        episode_accuracies.append(avg_accuracy)
    
    # Calculate statistics
    results = {
        'algorithm': baseline.name,
        'num_episodes': num_episodes,
        'avg_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'avg_energy': np.mean(episode_energies),
        'std_energy': np.std(episode_energies),
        'avg_delay': np.mean(episode_delays),
        'std_delay': np.std(episode_delays),
        'avg_accuracy': np.mean(episode_accuracies),
        'std_accuracy': np.std(episode_accuracies),
        'constraint_violations': constraint_violations,
        'violation_rate': {
            k: v / (num_episodes * env.num_devices) 
            for k, v in constraint_violations.items()
        },
        'episode_rewards': episode_rewards,
        'episode_energies': episode_energies,
        'episode_delays': episode_delays,
        'episode_accuracies': episode_accuracies
    }
    
    return results
